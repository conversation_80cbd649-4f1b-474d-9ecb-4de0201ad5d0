# Câu trả lời về hệ thống quản lý Clubs và Competitions

## Tóm tắt câu hỏi từ khách hàng

**Khách hàng muốn xác nhận:**
1. <PERSON><PERSON><PERSON> tạo mới và cập nhật thông tin **Clubs (câu lạc bộ)**
2. C<PERSON>ch tạo mới và cập nhật thông tin **Competitions (giải đấu)**

**Bối cảnh:**
- Vấn đề logo đội bóng nước ngoài trong FIFA Club World Cup chưa được đăng ký
- Cần thay đổi tên giải đấu do sponsor khác nhau

---

## ① Về Clubs (Câu lạc bộ)

### Cách tạo mới và cập nhật

**🔄 Tự động từ Data Stadium:**
- Clubs được import tự động từ file XML của Data Stadium
- **File nguồn**: `/master/team_directory-{COMPETITION_ID}.xml`
- **Thời điể<PERSON> cập nhật**: 
  - Mỗi 15 phút (full data): `*/15 * * * * import_ds_data yes yes`
  - Mỗi phút (live data): `*/1 * * * * import_ds_data yes no`

**📝 Cơ chế hoạt động:**
```python
async def import_clubs(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import clubs from DS
    source file: /master/team_directory-{COMPETITION_ID}.xml
    """
    for team_node in root.findall("TeamDirectory/TeamInfo"):
        club_id = xml_value(team_node, "TeamID", int)
        name = xml_value(team_node, "TeamName")
        name_short = xml_value(team_node, "TeamNameS")
        
        # Tự động tạo mới hoặc cập nhật club
        club, _ = await async_upsert(
            Club,
            ds_club_id=club_id,
            defaults={
                "name": name,
                "name_short": name_short,
                "slug": get_team_slug(club_id, name_short),
                # ... các thông tin khác
            },
        )
```

### Quản lý logo/emblem

**📁 Lưu trữ logo:**
- Logo được lưu trữ dưới dạng file tĩnh trong: `static/images/clubs/emblems/`
- Format hỗ trợ: SVG (ưu tiên) hoặc PNG

**🏷️ Template tag xử lý logo:**
```python
@register.simple_tag
def club_emblem_file(slug, *args, **kwargs):
    png_list = (
        "barcelona", "bayern", "celtic", "leon", "manchesterc",
        "muangthong", "paris-sg", "pathumunited", "tottenham",
        "stuttgart", "newcastle",
    )
    if slug in png_list:
        return f"{slug}.png"
    return f"{slug}.svg"
```

**✅ Giải pháp cho logo đội nước ngoài:**
1. **Tự quản lý logo**: Thêm file logo vào thư mục `static/images/clubs/emblems/`
2. **Tên file**: Sử dụng slug của club (ví dụ: `real-madrid.svg`)
3. **Không cần Data Stadium**: Hệ thống tự quản lý logo độc lập
4. **Cập nhật code**: Thêm vào `png_list` nếu cần dùng format PNG

---

## ② Về Competitions (Giải đấu)

### Cách tạo mới và cập nhật

**🔄 Tự động từ Data Stadium:**
- Competitions được import từ file XML của Data Stadium
- **File nguồn**: `/master/mst_gamekind.xml`
- **Thời điểm cập nhật**: Cùng chu kỳ với clubs (mỗi 15 phút)

**📝 Cơ chế hoạt động:**
```python
async def import_game_kinds(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import competitions from DS
    source file: master/mst_gamekind.xml
    """
    for node in root.findall("GameKindMST/GameKindInfo"):
        id = xml_value(node, "GameKindID", int)
        name = xml_value(node, "GameKindName")
        name_short = xml_value(node, "GameKindNameS")
        
        await async_upsert(
            Competition,
            id=id,
            defaults={
                "name": name,
                "name_short": name_short,
                "slug": get_competition_slug(id, name_short),
            },
        )
```

### Quản lý tên giải đấu với sponsor khác nhau

**🌐 Translation System:**
- Hệ thống có hỗ trợ đa ngôn ngữ cho tên giải đấu
- Field `t_name` và `t_name_short` cho phép override tên hiển thị

**⚙️ Cách quản lý qua Wagtail Admin:**
1. Vào menu **Core** → **Competitions**
2. Chọn competition cần chỉnh sửa
3. Cập nhật field `t_name` với tên mới (có sponsor)
4. Lưu thay đổi

**📋 Model Competition:**
```python
class Competition(models.Model):
    name = models.CharField(max_length=255)  # Từ Data Stadium
    name_short = models.CharField(max_length=255)  # Từ Data Stadium
    slug = models.SlugField(unique=True, db_index=True)
    t_name = JSONField(schema=TRANSLATION_VALUES_SCHEMA)  # Override tên
    t_name_short = JSONField(schema=TRANSLATION_VALUES_SCHEMA)  # Override tên ngắn
```

**✅ Giải pháp cho tên giải đấu:**
1. **Sử dụng translation system**: Override tên qua Wagtail Admin
2. **Không cần Data Stadium**: Tự quản lý tên hiển thị
3. **Đa ngôn ngữ**: Hỗ trợ tên khác nhau cho từng ngôn ngữ
4. **Linh hoạt**: Có thể thay đổi theo sponsor mà không ảnh hưởng data gốc

---

## 🎯 Kết luận và Khuyến nghị

### Cho vấn đề logo đội nước ngoài:
- ✅ **Tự quản lý được**: Không cần nhờ Data Stadium
- ✅ **Dễ thực hiện**: Chỉ cần thêm file vào thư mục static
- ✅ **Linh hoạt**: Có thể cập nhật bất cứ lúc nào

### Cho vấn đề tên giải đấu:
- ✅ **Translation system**: Hỗ trợ override tên hiển thị
- ✅ **Không ảnh hưởng data gốc**: Giữ nguyên data từ Data Stadium
- ✅ **Quản lý qua Admin**: Dễ dàng thay đổi qua giao diện

### Tóm tắt:
**Cả hai vấn đề đều có thể giải quyết hoàn toàn độc lập, không cần sự hỗ trợ từ Data Stadium. Hệ thống đã được thiết kế với đủ tính năng để tự quản lý các customization này.**
