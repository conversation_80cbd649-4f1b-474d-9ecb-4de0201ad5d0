# J-League Data Flow

```mermaid
graph LR
    A[Data Stadium FTP] --> B[FTP Import System]
    B --> C[Django Models]
    C --> D[PostgreSQL Database]
    
    E[WSC Videos] --> F[Video Import]
    F --> C
    
    G[Google Sheets] --> H[Translation Import]
    H --> C
    
    C --> I[Cache Layer Redis]
    C --> J[API Endpoints]
    C --> K[WebSocket Updates]
    
    I --> L[Frontend Templates]
    J --> M[Mobile Apps]
    K --> N[Live Match Updates]
    
    D --> O[Backup System]
    O --> P[S3 Storage]
```
